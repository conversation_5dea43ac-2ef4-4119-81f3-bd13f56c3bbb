#!/bin/bash

# SSL Certificate Renewal Script
# Run this script periodically to renew SSL certificates

set -e

echo "🔄 Checking SSL certificate renewal for rahulbhoyar.com..."

# Check if certificates exist
if [ ! -f "certbot/conf/live/rahulbhoyar.com/fullchain.pem" ]; then
    echo "❌ No certificates found. Run ./setup-ssl.sh first."
    exit 1
fi

# Check certificate expiry (renew if less than 30 days remaining)
echo "📅 Checking certificate expiry..."
if docker run --rm -v "$(pwd)/certbot/conf:/etc/letsencrypt" certbot/certbot certificates | grep -q "VALID"; then
    # Try to renew certificates
    echo "🔄 Attempting certificate renewal..."
    
    # Ensure nginx is running for webroot challenge
    if ! docker compose -f docker-compose.prod.yml ps | grep -q "frontend-container.*Up"; then
        echo "⚠️  Frontend container not running. Starting it for renewal..."
        docker compose -f docker-compose.prod.yml up -d frontend
        sleep 10
    fi
    
    # Attempt renewal
    docker run --rm \
        -v "$(pwd)/certbot/conf:/etc/letsencrypt" \
        -v "$(pwd)/certbot/www:/var/www/certbot" \
        certbot/certbot renew --webroot -w /var/www/certbot --quiet
    
    # Reload nginx to use new certificates
    if docker compose -f docker-compose.prod.yml ps | grep -q "frontend-container.*Up"; then
        echo "🔄 Reloading nginx with new certificates..."
        docker compose -f docker-compose.prod.yml exec frontend nginx -s reload
    fi
    
    echo "✅ Certificate renewal completed successfully!"
else
    echo "⚠️  Certificate status check failed. Manual intervention may be required."
    docker run --rm -v "$(pwd)/certbot/conf:/etc/letsencrypt" certbot/certbot certificates
fi

echo "📋 Current certificate status:"
docker run --rm -v "$(pwd)/certbot/conf:/etc/letsencrypt" certbot/certbot certificates
