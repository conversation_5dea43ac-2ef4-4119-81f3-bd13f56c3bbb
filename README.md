# <PERSON><PERSON>yar Portfolio Website

## Author: <PERSON><PERSON>
*Updated with SSL/HTTPS security enhancements*

## Description

This repository contains a modern, responsive portfolio website for <PERSON><PERSON> Bhoyar, showcasing professional experience, projects, publications, and technical skills. The website features a sleek dark mode design with interactive elements and a user-friendly interface. Built with React for the frontend and FastAPI for the backend, the application is containerized using Docker and orchestrated with Docker Compose.

![Portfolio Screenshot](assets/Rahul-Bhoyar.png)

---

## Features

- **Responsive Design**: Fully responsive layout that works on mobile, tablet, and desktop devices
- **Dark Mode**: Elegant dark theme with carefully selected color schemes
- **Interactive UI**: Smooth animations and transitions for an engaging user experience
- **Project Showcase**: Detailed project cards with descriptions, technologies, and links
- **Publications Section**: Academic and professional publications with citation information
- **Blog**: Technical blog with articles and tutorials
- **Contact Form**: Easy-to-use contact form with validation
- **Social Media Integration**: Links to various professional profiles (GitHub, LinkedIn, <PERSON>ggle, HuggingFace, etc.)
- **SEO Optimized**: Meta tags and structured data for better search engine visibility

---

## Tech Stack

### Frontend
- **React 17+**: Modern JavaScript library for building user interfaces
- **React Router 6**: For seamless navigation and routing
- **React Bootstrap**: UI component library for responsive design
- **React Icons**: Comprehensive icon library
- **CSS3**: Custom styling with advanced CSS features
- **Axios**: Promise-based HTTP client for API requests

### Backend
- **FastAPI**: High-performance Python web framework
- **Pydantic**: Data validation and settings management
- **Uvicorn**: ASGI server for Python web applications

### DevOps & Security
- **Docker**: Containerization for consistent development and deployment
- **Docker Compose**: Multi-container orchestration
- **Nginx**: High-performance web server and reverse proxy
- **Let's Encrypt**: Free SSL/TLS certificates for HTTPS
- **SSL/TLS**: End-to-end encryption with modern security headers

---

## 🔐 SSL/HTTPS Setup

This website is configured with automatic SSL certificate generation and renewal using Let's Encrypt. The production environment enforces HTTPS with security headers.

### Production SSL Features
- ✅ Automatic SSL certificate generation
- ✅ HTTP to HTTPS redirect
- ✅ Modern TLS protocols (TLS 1.2, TLS 1.3)
- ✅ Security headers (HSTS, X-Frame-Options, etc.)
- ✅ Automatic certificate renewal
- ✅ Certificate monitoring and diagnostics

### SSL Management Scripts

#### Initial SSL Setup
```bash
./setup-ssl.sh
```
Generates SSL certificates for the first time. This is automatically called by `start-prod.sh` if certificates don't exist.

#### Certificate Renewal
```bash
./renew-ssl.sh
```
Manually renew SSL certificates. Certificates are also auto-renewed every 12 hours in production.

#### SSL Diagnostics
```bash
./check-ssl.sh
```
Comprehensive SSL troubleshooting tool that checks:
- Certificate file existence and validity
- DNS resolution
- Container status
- SSL connection testing
- Nginx configuration

### Troubleshooting SSL Issues

If you see "unsecured connection" warnings:

1. **Check certificate status**: `./check-ssl.sh`
2. **Regenerate certificates**: `./setup-ssl.sh`
3. **Verify DNS**: Ensure domain points to your server
4. **Check logs**: `docker compose -f docker-compose.prod.yml logs`

---