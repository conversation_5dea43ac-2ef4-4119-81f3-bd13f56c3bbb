#!/bin/bash

# SSL Troubleshooting Script
# Use this script to diagnose SSL certificate issues

echo "🔍 SSL Certificate Diagnostic Tool for rahulbhoyar.com"
echo "=================================================="

# Check if certificate files exist
echo ""
echo "📁 Checking certificate files..."
if [ -d "certbot/conf/live/rahulbhoyar.com" ]; then
    echo "✅ Certificate directory exists"
    ls -la certbot/conf/live/rahulbhoyar.com/
else
    echo "❌ Certificate directory not found"
    echo "💡 Run ./setup-ssl.sh to generate certificates"
fi

# Check certificate validity
echo ""
echo "📅 Checking certificate validity..."
if [ -f "certbot/conf/live/rahulbhoyar.com/fullchain.pem" ]; then
    docker run --rm -v "$(pwd)/certbot/conf:/etc/letsencrypt" certbot/certbot certificates
else
    echo "❌ Certificate files not found"
fi

# Check domain DNS resolution
echo ""
echo "🌐 Checking DNS resolution..."
echo "rahulbhoyar.com:"
nslookup rahulbhoyar.com || echo "❌ DNS resolution failed for rahulbhoyar.com"
echo "www.rahulbhoyar.com:"
nslookup www.rahulbhoyar.com || echo "❌ DNS resolution failed for www.rahulbhoyar.com"

# Check if containers are running
echo ""
echo "🐳 Checking Docker containers..."
docker compose -f docker-compose.prod.yml ps

# Test SSL connection
echo ""
echo "🔐 Testing SSL connection..."
echo "Testing rahulbhoyar.com:443..."
timeout 10 openssl s_client -connect rahulbhoyar.com:443 -servername rahulbhoyar.com < /dev/null 2>/dev/null | grep -E "(subject|issuer|notAfter)" || echo "❌ SSL connection test failed"

# Check nginx configuration
echo ""
echo "⚙️  Checking nginx configuration..."
if docker compose -f docker-compose.prod.yml ps | grep -q "frontend-container.*Up"; then
    echo "✅ Frontend container is running"
    docker compose -f docker-compose.prod.yml exec frontend nginx -t || echo "❌ Nginx configuration test failed"
else
    echo "❌ Frontend container is not running"
fi

echo ""
echo "🎯 Quick fixes:"
echo "1. If certificates are missing: ./setup-ssl.sh"
echo "2. If certificates are expired: ./renew-ssl.sh"
echo "3. If containers are down: ./start-prod.sh"
echo "4. Check logs: docker compose -f docker-compose.prod.yml logs"
