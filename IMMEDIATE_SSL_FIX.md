# 🚨 IMMEDIATE SSL CERTIFICATE DATE FIX

## Problem: `net::ERR_CERT_DATE_INVALID`

Your SSL certificate has an invalid date. This usually happens when:
1. The certificate was generated with incorrect system time
2. The server's clock is out of sync
3. The certificate is expired or not yet valid

## 🔧 IMMEDIATE FIX (Run on Production Server)

### Step 1: Check System Time
```bash
cd Rahul-Bhoyar-Website

# Check current time
date
date -u

# If time is wrong, sync it (Ubuntu/Debian)
sudo timedatectl set-ntp true
sudo systemctl restart systemd-timesyncd

# Or manually sync (if needed)
sudo ntpdate -s time.nist.gov
```

### Step 2: Regenerate SSL Certificates
```bash
# Make scripts executable
chmod +x *.sh

# Force regenerate certificates with correct time
./regenerate-ssl.sh
```

### Step 3: Alternative Quick Fix
If the above doesn't work, try this manual approach:
```bash
# Stop containers
docker compose -f docker-compose.prod.yml down

# Remove bad certificates
rm -rf certbot/conf/live/rahulbhoyar.com
rm -rf certbot/conf/archive/rahulbhoyar.com
rm -f certbot/conf/renewal/rahulbhoyar.com.conf

# Ensure directories exist
mkdir -p certbot/conf certbot/www

# Generate certificates manually
docker run --rm \
  -v "$(pwd)/certbot/conf:/etc/letsencrypt" \
  -v "$(pwd)/certbot/www:/var/www/certbot" \
  -p 80:80 \
  certbot/certbot certonly \
  --standalone \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  --force-renewal \
  -d rahulbhoyar.com \
  -d www.rahulbhoyar.com

# Start production
./start-prod.sh
```

## 🔍 Verify Fix

After running the fix:
```bash
# Check certificate dates
openssl x509 -in certbot/conf/live/rahulbhoyar.com/fullchain.pem -noout -dates

# Test SSL connection
curl -I https://rahulbhoyar.com

# Check in browser (may need to clear cache/try incognito)
```

## 🎯 Expected Results

- ✅ Certificate shows current valid dates
- ✅ Website loads with green padlock
- ✅ No more `ERR_CERT_DATE_INVALID` error

## 📞 If Still Not Working

1. **Clear browser cache** completely
2. **Try incognito/private mode**
3. **Check from different device/network**
4. **Verify DNS**: `nslookup rahulbhoyar.com`
5. **Test SSL**: https://www.ssllabs.com/ssltest/analyze.html?d=rahulbhoyar.com
