# 🚨 Production Deployment Troubleshooting

## Current Issue: Git Merge Conflict

The deployment is failing because there are local changes to `start-prod.sh` on the production server that conflict with the new SSL setup changes.

## 🔧 Quick Fix (Run on Production Server)

### Option 1: Automatic Resolution (Recommended)
```bash
cd Rahul-Bhoyar-Website

# Stash any local changes
git stash push -m "Auto-stash before SSL update $(date)"

# Pull the latest changes
git pull origin master

# Make scripts executable
chmod +x start-prod.sh setup-ssl.sh renew-ssl.sh check-ssl.sh

# Deploy with SSL setup
./start-prod.sh
```

### Option 2: Manual Resolution
```bash
cd Rahul-Bhoyar-Website

# Check what files have conflicts
git status

# Reset the conflicting file to match the repository
git checkout HEAD -- start-prod.sh

# Pull latest changes
git pull origin master

# Deploy
./start-prod.sh
```

### Option 3: Force Update (Use with caution)
```bash
cd Rahul-Bhoyar-Website

# Backup current state
cp start-prod.sh start-prod.sh.backup

# Force reset to match repository
git reset --hard HEAD
git pull origin master

# Deploy
./start-prod.sh
```

## 🔍 Verify SSL Setup

After successful deployment, check SSL status:
```bash
./check-ssl.sh
```

## 🌐 Expected Results

- Website accessible at: https://rahulbhoyar.com
- SSL certificate from Let's Encrypt
- Automatic HTTP to HTTPS redirect
- No more "unsecured connection" warnings

## 📞 If Issues Persist

1. Check container logs: `docker compose -f docker-compose.prod.yml logs`
2. Verify DNS: `nslookup rahulbhoyar.com`
3. Test SSL: `curl -I https://rahulbhoyar.com`
