#!/bin/bash

# SSL Certificate Setup Script for Production
# This script sets up SSL certificates for rahulbhoyar.com

set -e

echo "🔐 Setting up SSL certificates for rahulbhoyar.com..."

# Create necessary directories
echo "📁 Creating certificate directories..."
mkdir -p certbot/conf certbot/www

# Check if certificates already exist
if [ -f "certbot/conf/live/rahulbhoyar.com/fullchain.pem" ]; then
    echo "✅ SSL certificates already exist. Checking if renewal is needed..."
    
    # Check certificate expiry
    if docker run --rm -v "$(pwd)/certbot/conf:/etc/letsencrypt" certbot/certbot certificates | grep -q "VALID"; then
        echo "✅ Certificates are valid. Skipping generation."
        exit 0
    else
        echo "⚠️  Certificates need renewal or are invalid."
    fi
fi

echo "🚀 Starting initial certificate generation process..."

# Step 1: Start temporary nginx for ACME challenge
echo "1️⃣  Starting temporary nginx for ACME challenge..."
docker compose -f docker-compose.prod.yml --profile init up -d nginx-init

# Wait for nginx to be ready
echo "⏳ Waiting for nginx to be ready..."
sleep 10

# Step 2: Generate SSL certificates
echo "2️⃣  Generating SSL certificates with Let's Encrypt..."
docker compose -f docker-compose.prod.yml --profile init run --rm certbot

# Step 3: Stop temporary nginx
echo "3️⃣  Stopping temporary nginx..."
docker compose -f docker-compose.prod.yml --profile init down

# Step 4: Verify certificates were created
if [ -f "certbot/conf/live/rahulbhoyar.com/fullchain.pem" ]; then
    echo "✅ SSL certificates generated successfully!"
    echo "📋 Certificate details:"
    docker run --rm -v "$(pwd)/certbot/conf:/etc/letsencrypt" certbot/certbot certificates
else
    echo "❌ Failed to generate SSL certificates!"
    echo "🔍 Please check the logs above for errors."
    exit 1
fi

echo "🎉 SSL setup completed successfully!"
echo "💡 You can now start the production environment with: ./start-prod.sh"
