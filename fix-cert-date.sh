#!/bin/bash

# SSL Certificate Date Fix Script
# This script diagnoses and fixes certificate date issues

set -e

echo "🔍 Diagnosing SSL certificate date issues for rahulbhoyar.com..."

# Check system time first
echo ""
echo "⏰ Checking system time..."
echo "Current system time: $(date)"
echo "Current UTC time: $(date -u)"

# Check if certificates exist
if [ ! -f "certbot/conf/live/rahulbhoyar.com/fullchain.pem" ]; then
    echo "❌ SSL certificates not found!"
    echo "🔧 Generating new certificates..."
    ./setup-ssl.sh
    exit 0
fi

# Check certificate dates
echo ""
echo "📅 Checking certificate validity dates..."
docker run --rm -v "$(pwd)/certbot/conf:/etc/letsencrypt" certbot/certbot certificates

# Check certificate details with openssl
echo ""
echo "🔍 Detailed certificate analysis..."
if [ -f "certbot/conf/live/rahulbhoyar.com/fullchain.pem" ]; then
    echo "Certificate validity period:"
    openssl x509 -in certbot/conf/live/rahulbhoyar.com/fullchain.pem -noout -dates
    
    echo ""
    echo "Certificate subject and issuer:"
    openssl x509 -in certbot/conf/live/rahulbhoyar.com/fullchain.pem -noout -subject -issuer
fi

# Check if certificate is expired or not yet valid
echo ""
echo "🕐 Certificate time validation..."
if openssl x509 -in certbot/conf/live/rahulbhoyar.com/fullchain.pem -checkend 0 -noout; then
    echo "✅ Certificate is currently valid (not expired)"
else
    echo "❌ Certificate is expired or not yet valid!"
    echo "🔧 Regenerating certificate..."
    
    # Force certificate renewal
    docker run --rm \
        -v "$(pwd)/certbot/conf:/etc/letsencrypt" \
        -v "$(pwd)/certbot/www:/var/www/certbot" \
        certbot/certbot renew --force-renewal --webroot -w /var/www/certbot
    
    # Reload nginx
    if docker compose -f docker-compose.prod.yml ps | grep -q "frontend-container.*Up"; then
        echo "🔄 Reloading nginx with new certificate..."
        docker compose -f docker-compose.prod.yml exec frontend nginx -s reload
    fi
    
    echo "✅ Certificate regenerated successfully!"
fi

# Test SSL connection
echo ""
echo "🔐 Testing SSL connection..."
timeout 10 openssl s_client -connect rahulbhoyar.com:443 -servername rahulbhoyar.com < /dev/null 2>/dev/null | grep -E "(subject|issuer|notBefore|notAfter)" || echo "❌ SSL connection test failed"

echo ""
echo "🎯 If the issue persists:"
echo "1. Check if your server's system time is correct"
echo "2. Verify DNS propagation: nslookup rahulbhoyar.com"
echo "3. Try accessing: https://www.ssllabs.com/ssltest/analyze.html?d=rahulbhoyar.com"
echo "4. Clear browser cache and try incognito mode"
