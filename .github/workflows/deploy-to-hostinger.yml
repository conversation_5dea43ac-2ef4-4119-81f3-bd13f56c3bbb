name: 🚀 Deploy to <PERSON><PERSON>

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy via SSH
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.HOSTINGER_HOST }}
          username: ${{ secrets.HOSTINGER_USER }}
          key: ${{ secrets.HOSTINGER_SSH_KEY }}
          script: |
            set -e  # Exit immediately if a command exits with a non-zero status
            cd ..
            cd Rahul-Bhoyar-Website
            git pull

            # Export secrets safely with quotes for special characters
            export GROQ_API_KEY='${{ secrets.GROQ_API_KEY }}'
            export GMAIL='${{ secrets.GMAIL }}'
            export GMAIL_PASSWORD='${{ secrets.GMAIL_PASSWORD }}'
            export RECIPIENT_EMAIL_1='${{ secrets.RECIPIENT_EMAIL_1 }}'
            export MONGODB_DATABASE_URL='${{ secrets.MONGODB_DATABASE_URL }}'

            echo "✅ Environment variables set:"
            echo "GMAIL=$GMAIL"
            echo "RECIPIENT_EMAIL_1=$RECIPIENT_EMAIL_1"
            echo "MONGODB_DATABASE_URL=${MONGODB_DATABASE_URL:0:20}..."  # Partial output for security
            echo "GROQ_API_KEY=${GROQ_API_KEY:0:10}..."  # Partial output for security
            echo "GMAIL_PASSWORD=********"

            # Make scripts executable
            chmod +x start-prod.sh setup-ssl.sh renew-ssl.sh check-ssl.sh

            # Start production environment (includes SSL setup)
            bash start-prod.sh
