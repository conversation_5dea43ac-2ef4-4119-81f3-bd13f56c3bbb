name: 🚀 Deploy to <PERSON><PERSON>

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy via SSH
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.HOSTINGER_HOST }}
          username: ${{ secrets.HOSTINGER_USER }}
          key: ${{ secrets.HOSTINGER_SSH_KEY }}
          script: |
            set -e  # Exit immediately if a command exits with a non-zero status
            cd ..
            cd Rahul-Bhoyar-Website

            # Handle potential merge conflicts by stashing local changes
            echo "🔄 Handling potential merge conflicts..."
            git stash push -m "Auto-stash before deployment $(date)"

            # Pull latest changes
            echo "📥 Pulling latest changes..."
            git pull origin master

            # If there were stashed changes, we can optionally restore them
            # For production deployment, we typically want to use the latest code
            echo "✅ Repository updated successfully"

            # Export secrets safely with quotes for special characters
            export GROQ_API_KEY='${{ secrets.GROQ_API_KEY }}'
            export GMAIL='${{ secrets.GMAIL }}'
            export GMAIL_PASSWORD='${{ secrets.GMAIL_PASSWORD }}'
            export RECIPIENT_EMAIL_1='${{ secrets.RECIPIENT_EMAIL_1 }}'
            export MONGODB_DATABASE_URL='${{ secrets.MONGODB_DATABASE_URL }}'

            echo "✅ Environment variables set:"
            echo "GMAIL=$GMAIL"
            echo "RECIPIENT_EMAIL_1=$RECIPIENT_EMAIL_1"
            echo "MONGODB_DATABASE_URL=${MONGODB_DATABASE_URL:0:20}..."  # Partial output for security
            echo "GROQ_API_KEY=${GROQ_API_KEY:0:10}..."  # Partial output for security
            echo "GMAIL_PASSWORD=********"

            # Make scripts executable
            echo "🔧 Making scripts executable..."
            chmod +x start-prod.sh setup-ssl.sh renew-ssl.sh check-ssl.sh

            # Verify scripts exist
            if [ ! -f "start-prod.sh" ]; then
                echo "❌ start-prod.sh not found!"
                exit 1
            fi

            # Start production environment (includes SSL setup)
            echo "🚀 Starting production deployment..."
            bash start-prod.sh

            # Check deployment status
            echo "✅ Deployment completed successfully!"
            echo "🌐 Website should be accessible at https://rahulbhoyar.com"
