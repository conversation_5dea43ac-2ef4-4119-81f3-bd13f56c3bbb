# # Step 1: Build the React app
# FROM node:16 AS build
# WORKDIR /app
# COPY package*.json ./
# RUN npm install
# COPY . .
# RUN npm run build

# # Step 2: Serve with NGINX
# FROM nginx:alpine

# # Remove default NGINX config
# RUN rm -f /etc/nginx/conf.d/default.conf

# # Copy custom NGINX config from frontend folder
# COPY rahulbhoyar.com.conf /etc/nginx/conf.d/

# # Copy frontend build to NGINX public folder
# COPY --from=build /app/build /usr/share/nginx/html

# EXPOSE 80
# CMD ["nginx", "-g", "daemon off;"]



# Step 1: Build the React app
FROM node:16 AS build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

# Step 2: Serve with Nginx
FROM nginx:alpine

# Remove default Nginx config
RUN rm -f /etc/nginx/conf.d/default.conf

# Copy your prod config from nginx folder (adjust path if needed)
COPY rahulbhoyar.com.conf /etc/nginx/conf.d/

# Copy the React build to Nginx's default web root
COPY --from=build /app/build /usr/share/nginx/html

EXPOSE 80 443
CMD ["nginx", "-g", "daemon off;"]
