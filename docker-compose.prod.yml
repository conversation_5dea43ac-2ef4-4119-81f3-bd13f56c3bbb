services:
  # Temporary nginx for initial certificate generation
  nginx-init:
    image: nginx:alpine
    container_name: nginx-init
    ports:
      - "80:80"
    volumes:
      - ./certbot/www:/var/www/certbot
    command: >
      sh -c "echo 'server {
        listen 80;
        server_name rahulbhoyar.com www.rahulbhoyar.com;
        location /.well-known/acme-challenge/ {
          root /var/www/certbot;
        }
        location / {
          return 301 https://$$host$$request_uri;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    profiles:
      - init

  certbot:
    image: certbot/certbot
    container_name: certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    environment:
      - TZ=UTC
    entrypoint: >
      sh -c "echo 'Current time:' && date &&
      certbot certonly --webroot -w /var/www/certbot
      --email <EMAIL>
      --agree-tos --no-eff-email
      --non-interactive --keep-until-expiring
      --preferred-challenges http
      --rsa-key-size 4096
      -d rahulbhoyar.com -d www.rahulbhoyar.com"
    profiles:
      - init

  certbot-renew:
    image: certbot/certbot
    container_name: certbot-renew
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    entrypoint: >
      sh -c "while :; do certbot renew --webroot -w /var/www/certbot --quiet; sleep 12h; done"
  

  frontend:
    container_name: frontend-container
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - portfolio-network


  backend: # FastAPI Backend
    container_name: backend-container
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    # No direct port exposure to host - only through nginx
    expose:
      - "8000"
    volumes:
      - ./backend:/app
    networks:
      - portfolio-network
    environment:
      - CORS_ORIGINS=http://localhost
      - GROQ_API_KEY=${GROQ_API_KEY}
      - GMAIL=${GMAIL} 
      - GMAIL_PASSWORD=${GMAIL_PASSWORD} 
      - RECIPIENT_EMAIL_1=${RECIPIENT_EMAIL_1}
      - MONGODB_DATABASE_URL=${MONGODB_DATABASE_URL}
      - MONGODB_DATABASE_NAME=website-data
    extra_hosts:
      - "host.docker.internal:host-gateway"

# volumes:
#   #ollama_models:
#   pgvector_data:
#     driver: local

networks:
  portfolio-network:
    driver: bridge
