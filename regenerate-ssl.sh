#!/bin/bash

# Force SSL Certificate Regeneration Script
# Use this when certificates have date/validity issues

set -e

echo "🔄 Force regenerating SSL certificates for rahulbhoyar.com..."

# Check system time
echo "⏰ System time check:"
echo "Local time: $(date)"
echo "UTC time: $(date -u)"

# Stop production containers
echo "🛑 Stopping production containers..."
docker compose -f docker-compose.prod.yml down

# Remove existing certificates
echo "🗑️  Removing existing certificates..."
if [ -d "certbot/conf/live/rahulbhoyar.com" ]; then
    rm -rf certbot/conf/live/rahulbhoyar.com
    echo "✅ Old certificates removed"
fi

if [ -d "certbot/conf/archive/rahulbhoyar.com" ]; then
    rm -rf certbot/conf/archive/rahulbhoyar.com
    echo "✅ Certificate archive removed"
fi

if [ -f "certbot/conf/renewal/rahulbhoyar.com.conf" ]; then
    rm -f certbot/conf/renewal/rahulbhoyar.com.conf
    echo "✅ Renewal configuration removed"
fi

# Ensure directories exist
mkdir -p certbot/conf certbot/www

# Start temporary nginx for ACME challenge
echo "🚀 Starting temporary nginx for certificate generation..."
docker compose -f docker-compose.prod.yml --profile init up -d nginx-init

# Wait for nginx to be ready
echo "⏳ Waiting for nginx to be ready..."
sleep 15

# Test if nginx is responding
if curl -f http://rahulbhoyar.com/.well-known/acme-challenge/test 2>/dev/null; then
    echo "✅ Nginx is ready for ACME challenge"
else
    echo "⚠️  Nginx might not be fully ready, but proceeding..."
fi

# Generate new certificates with force renewal
echo "🔐 Generating new SSL certificates..."
docker compose -f docker-compose.prod.yml --profile init run --rm certbot

# Stop temporary nginx
echo "🛑 Stopping temporary nginx..."
docker compose -f docker-compose.prod.yml --profile init down

# Verify new certificates
if [ -f "certbot/conf/live/rahulbhoyar.com/fullchain.pem" ]; then
    echo "✅ New certificates generated successfully!"
    
    # Show certificate details
    echo "📋 New certificate details:"
    openssl x509 -in certbot/conf/live/rahulbhoyar.com/fullchain.pem -noout -dates -subject -issuer
    
    # Start production environment
    echo "🚀 Starting production environment..."
    docker compose -f docker-compose.prod.yml up --build -d
    
    # Wait for services
    sleep 20
    
    # Test SSL
    echo "🔍 Testing new SSL certificate..."
    timeout 10 openssl s_client -connect rahulbhoyar.com:443 -servername rahulbhoyar.com < /dev/null 2>/dev/null | grep -E "(subject|issuer|notAfter)" || echo "⚠️  SSL test inconclusive"
    
    echo "🎉 Certificate regeneration completed!"
    echo "🌐 Test your website: https://rahulbhoyar.com"
    
else
    echo "❌ Failed to generate new certificates!"
    echo "🔍 Check the logs above for errors"
    exit 1
fi
