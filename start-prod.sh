#!/bin/bash

# Production Deployment Script with SSL Support
echo "🚀 Starting production deployment for rahulbhoyar.com..."

# Stop any running containers
echo "🛑 Stopping any running containers..."
docker compose -f docker-compose.prod.yml down

# Check if SSL certificates exist
if [ ! -f "certbot/conf/live/rahulbhoyar.com/fullchain.pem" ]; then
    echo "🔐 SSL certificates not found. Running SSL setup..."
    chmod +x setup-ssl.sh
    ./setup-ssl.sh

    if [ $? -ne 0 ]; then
        echo "❌ SSL setup failed. Cannot start production environment."
        exit 1
    fi
else
    echo "✅ SSL certificates found."
fi


##### Docker Model Runner LLM Models
# LLM_MODEL_NAME="ai/llama3.2:1B-Q8_0"

# # Check if LLM_MODEL_NAME was found
# if [ -z "$LLM_MODEL_NAME" ]; then
#     echo "Error: LLM_MODEL_NAME not found."
#     exit 1
# fi

# echo "Using LLM model: $LLM_MODEL_NAME"

# # Pull the Docker model
# echo "Pulling Docker model..."
# docker model pull $LLM_MODEL_NAME


# Build and start the containers in production mode
echo "🏗️  Building and starting containers in production mode..."
docker compose -f docker-compose.prod.yml up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 15

# Check if services are running
if docker compose -f docker-compose.prod.yml ps | grep -q "Up"; then
    echo "✅ Production environment started successfully!"
    echo "🌐 Your website should now be accessible at:"
    echo "   • https://rahulbhoyar.com"
    echo "   • https://www.rahulbhoyar.com"
    echo ""
    echo "📊 Service status:"
    docker compose -f docker-compose.prod.yml ps
else
    echo "❌ Some services failed to start. Check the logs:"
    docker compose -f docker-compose.prod.yml logs
    exit 1
fi



set -e

##### Ollama LLM Models

# MODELS="mistral:7b"

# model_exists() {
#     docker compose -f docker-compose.prod.yml exec ollama ollama list | grep -q "$1"
# }

# for MODEL_NAME in $MODELS; do
#     if model_exists "$MODEL_NAME"; then
#         echo "Model $MODEL_NAME already exists."
#     else
#         echo "Pulling model $MODEL_NAME..."
#         docker compose -f docker-compose.prod.yml exec ollama ollama pull "$MODEL_NAME"
#     fi
# done
echo "All models are ready."
echo "Production environment is set up and running."